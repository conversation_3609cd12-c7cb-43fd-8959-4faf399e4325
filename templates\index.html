{% extends "base.html" %}

{% block title %}Guild Roster - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users text-accent me-2"></i>
                    Guild Roster
                </h1>
                <p class="text-secondary mb-0">Manage and track your guild members</p>
            </div>
            <div>
                <button id="refreshData" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-2"></i>Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card tank">
            <div class="stats-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stats-number role-tank-count">0</div>
            <div class="stats-label">Tanks</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card healer">
            <div class="stats-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stats-number role-healer-count">0</div>
            <div class="stats-label">Healers</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card melee">
            <div class="stats-icon">
                <i class="fas fa-sword"></i>
            </div>
            <div class="stats-number role-melee-count">0</div>
            <div class="stats-label">Melee DPS</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card ranged">
            <div class="stats-icon">
                <i class="fas fa-bow-arrow"></i>
            </div>
            <div class="stats-number role-ranged-count">0</div>
            <div class="stats-label">Ranged DPS</div>
        </div>
    </div>
</div>

<!-- Additional Stats Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="text-gold mb-1">
                    <span id="avgIlvl">0</span>
                </h5>
                <small class="text-secondary">Average iLvl</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="text-primary mb-1">
                    <span id="totalMembers">{{ characters|length }}</span>
                </h5>
                <small class="text-secondary">Total Members</small>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Role Distribution
                </h6>
            </div>
            <div class="card-body">
                <div style="height: 200px;">
                    <canvas id="roleChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Tier Tokens & Armor Types -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-gem me-2"></i>
                    Tier Token Distribution
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="tier-counter tier-mystic">
                            <span class="counter-value">0</span>
                            <small class="d-block">Mystic</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="tier-counter tier-dreadful">
                            <span class="counter-value">0</span>
                            <small class="d-block">Dreadful</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="tier-counter tier-venerated">
                            <span class="counter-value">0</span>
                            <small class="d-block">Venerated</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="tier-counter tier-zenith">
                            <span class="counter-value">0</span>
                            <small class="d-block">Zenith</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tshirt me-2"></i>
                    Armor Type Distribution
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="armor-counter armor-plate">
                            <span class="counter-value">0</span>
                            <small class="d-block">Plate</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="armor-counter armor-mail">
                            <span class="counter-value">0</span>
                            <small class="d-block">Mail</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="armor-counter armor-leather">
                            <span class="counter-value">0</span>
                            <small class="d-block">Leather</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="armor-counter armor-cloth">
                            <span class="counter-value">0</span>
                            <small class="d-block">Cloth</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Raid Buffs & Debuffs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    Raid Buffs & Debuffs Coverage
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-arrow-up me-1"></i>
                            Buffs
                        </h6>
                        <div class="d-flex flex-wrap gap-2">
                            <div class="buff-counter buff-druid" data-class="Druid" data-bs-toggle="tooltip" title="Mark of the Wild">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_regeneration.jpg" alt="Mark of the Wild" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-evoker" data-class="Evoker" data-bs-toggle="tooltip" title="Blessing of the Bronze">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/ability_evoker_blessingofthebronze.jpg" alt="Blessing of the Bronze" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-hunter" data-class="Hunter" data-bs-toggle="tooltip" title="Hunter's Mark">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_snipershot.jpg" alt="Hunter's Mark" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-mage" data-class="Mage" data-bs-toggle="tooltip" title="Arcane Intellect">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_magicalsentry.jpg" alt="Arcane Intellect" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-priest" data-class="Priest" data-bs-toggle="tooltip" title="Power Word: Fortitude">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_wordfortitude.jpg" alt="Power Word: Fortitude" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-shaman" data-class="Shaman" data-bs-toggle="tooltip" title="Skyfury">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/achievement_raidprimalist_windelemental.jpg" alt="Skyfury" class="buff-icon">
                            </div>
                            <div class="buff-counter buff-warrior" data-class="Warrior" data-bs-toggle="tooltip" title="Battle Shout">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_battleshout.jpg" alt="Battle Shout" class="buff-icon">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <h6 class="text-danger mb-3">
                            <i class="fas fa-arrow-down me-1"></i>
                            Debuffs
                        </h6>
                        <div class="d-flex flex-wrap gap-2">
                            <div class="debuff-counter debuff-demonhunter" data-class="Demon Hunter" data-bs-toggle="tooltip" title="Chaos Brand">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/ability_demonhunter_empowerwards.jpg" alt="Chaos Brand" class="debuff-icon">
                            </div>
                            <div class="debuff-counter debuff-monk" data-class="Monk" data-bs-toggle="tooltip" title="Mystic Touch">
                                <img src="https://wow.zamimg.com/images/wow/icons/large/ability_monk_palmstrike.jpg" alt="Mystic Touch" class="debuff-icon">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Character Roster Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Roster
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="ilvl">
                                    <i class="fas fa-star me-1"></i>iLvl
                                </th>
                                <th data-sort="role">
                                    <i class="fas fa-users-cog me-1"></i>Role
                                </th>
                                <th data-sort="class">
                                    <i class="fas fa-fist-raised me-1"></i>Class
                                </th>
                                <th data-sort="spec">
                                    <i class="fas fa-cog me-1"></i>Spec
                                </th>
                                <th data-sort="armor">
                                    <i class="fas fa-tshirt me-1"></i>Armor
                                </th>
                                <th data-sort="tier-pieces">
                                    <i class="fas fa-layer-group me-1"></i>Tier
                                </th>
                                <th data-sort="tier">
                                    <i class="fas fa-gem me-1"></i>Tier Token
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.url and character.url != '#' %}
                                            <a href="{{ character.url }}" target="_blank"
                                               class="text-decoration-none text-primary fw-bold">
                                                {{ character.name }}
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        {% else %}
                                            <span class="fw-bold">{{ character.name }}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="ilvl">{{ character.ilvl }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.role == "Tank" %}
                                            <i class="fas fa-shield-alt text-warning me-2"></i>
                                        {% elif character.role == "Healer" %}
                                            <i class="fas fa-heart text-success me-2"></i>
                                        {% elif character.role == "Melee DPS" or character.role == "Melee" %}
                                            <i class="fas fa-sword text-danger me-2"></i>
                                        {% else %}
                                            <i class="fas fa-bow-arrow text-info me-2"></i>
                                        {% endif %}
                                        <span class="role-{{ character.role.lower().replace(' ', '-') }}">
                                            {{ character.role }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="class-{{ character.class_name.lower().replace(' ', '') }} fw-bold">
                                        {{ character.class_name }}
                                    </span>
                                </td>
                                <td>{{ character.spec }}</td>
                                <td>
                                    <span class="badge bg-tertiary border-custom">
                                        {{ character.armor_type }}
                                    </span>
                                </td>
                                <td>
                                    <div class="tier-visual d-flex align-items-center gap-1">
                                        {% for i in range(5) %}
                                            <div class="tier-block {% if i < character.tier_pieces %}tier-filled{% else %}tier-empty{% endif %}"></div>
                                        {% endfor %}
                                        <span class="tier-count ms-2 small text-secondary">{{ character.tier_pieces }}/5</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-tertiary border-custom">
                                        {{ character.tier_token }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Modern counter update function
function updateCounters() {
    const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
    const roleCounters = {
        'Tank': 0,
        'Healer': 0,
        'Melee': 0,
        'Ranged': 0
    };
    const tierCounters = {
        'Mystic': 0,
        'Dreadful': 0,
        'Venerated': 0,
        'Zenith': 0
    };
    const armorCounters = {
        'Plate': 0,
        'Mail': 0,
        'Leather': 0,
        'Cloth': 0
    };

    const availableClasses = new Set();
    let totalIlvl = 0;
    let characterCount = 0;

    rows.forEach(row => {
        // Count roles (now column 2 instead of 3)
        const roleCell = row.cells[2]; // Role column
        const roleText = roleCell.textContent.trim();

        if (roleText.includes('Tank')) roleCounters['Tank']++;
        else if (roleText.includes('Healer')) roleCounters['Healer']++;
        else if (roleText.includes('Melee')) roleCounters['Melee']++;
        else if (roleText.includes('Ranged')) roleCounters['Ranged']++;

        // Count tier tokens (now column 7 instead of 7)
        const tierCell = row.cells[7]; // Tier token column
        const tierText = tierCell.textContent.trim();
        if (tierCounters.hasOwnProperty(tierText)) {
            tierCounters[tierText]++;
        }

        // Count armor types (now column 5 instead of 6)
        const armorCell = row.cells[5]; // Armor column
        const armorText = armorCell.textContent.trim();
        if (armorCounters.hasOwnProperty(armorText)) {
            armorCounters[armorText]++;
        }

        // Track available classes for buff/debuff counters (now column 3 instead of 4)
        const classCell = row.cells[3]; // Class column
        const className = classCell.textContent.trim();
        availableClasses.add(className);

        // Calculate average ilvl (now column 1 instead of 2)
        const ilvlCell = row.cells[1]; // iLvl column
        if (ilvlCell) {
            const ilvl = parseInt(ilvlCell.textContent.trim());
            if (!isNaN(ilvl)) {
                totalIlvl += ilvl;
                characterCount++;
            }
        }
    });

    // Update role counters in stats cards
    document.querySelector('.role-tank-count').textContent = roleCounters['Tank'];
    document.querySelector('.role-healer-count').textContent = roleCounters['Healer'];
    document.querySelector('.role-melee-count').textContent = roleCounters['Melee'];
    document.querySelector('.role-ranged-count').textContent = roleCounters['Ranged'];

    // Update tier counters
    Object.entries(tierCounters).forEach(([tier, count]) => {
        const counter = document.querySelector(`.tier-${tier.toLowerCase()} .counter-value`);
        if (counter) {
            counter.textContent = count;
        }
    });

    // Update armor counters
    Object.entries(armorCounters).forEach(([armor, count]) => {
        const counter = document.querySelector(`.armor-${armor.toLowerCase()} .counter-value`);
        if (counter) {
            counter.textContent = count;
        }
    });

    // Update average ilvl
    const avgIlvl = characterCount > 0 ? Math.round(totalIlvl / characterCount) : 0;
    const ilvlDisplay = document.getElementById('avgIlvl');
    if (ilvlDisplay) {
        ilvlDisplay.textContent = avgIlvl;
    }

    // Update buff/debuff counters
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        const requiredClass = counter.dataset.class;
        if (availableClasses.has(requiredClass)) {
            counter.classList.remove('unavailable');
            counter.classList.add('available');
        } else {
            counter.classList.remove('available');
            counter.classList.add('unavailable');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize buff counters as unavailable
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        counter.classList.add('unavailable');
    });

    // Update counters initially
    updateCounters();

    // Sort table by role initially
    const table = document.querySelector('.table-modern');
    if (table) {
        const roleHeader = table.querySelector('[data-sort="role"]');
        if (roleHeader) {
            roleHeader.click();
        }
    }
});
</script>

<!-- Additional CSS for buff/debuff styling -->
<style>
.buff-counter, .debuff-counter {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.buff-counter.available, .debuff-counter.available {
    border-color: var(--success-color);
    box-shadow: 0 0 10px rgba(35, 134, 54, 0.3);
    background: rgba(35, 134, 54, 0.1);
}

.buff-counter.unavailable, .debuff-counter.unavailable {
    border-color: #444 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    opacity: 0.3 !important;
    box-shadow: none !important;
}

.buff-icon, .debuff-icon {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
}

.buff-counter.unavailable .buff-icon,
.debuff-counter.unavailable .debuff-icon {
    filter: grayscale(100%) brightness(0.3) !important;
}

.tier-counter, .armor-counter {
    padding: 1rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.tier-counter .counter-value,
.armor-counter .counter-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.tier-counter small,
.armor-counter small {
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* Tier visualization blocks */
.tier-visual {
    min-width: 120px;
}

.tier-block {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.tier-filled {
    background-color: #dc3545;
    border-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
}

.tier-empty {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.tier-count {
    font-weight: 500;
    min-width: 30px;
}
</style>
{% endblock %}
